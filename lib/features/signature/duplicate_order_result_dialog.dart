import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'follow_up_record_page.dart';

class DuplicateOrderResultDialog extends StatelessWidget {
  final Map<String, dynamic> orderData;
  final String phone;

  const DuplicateOrderResultDialog({
    Key? key,
    required this.orderData,
    required this.phone,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final maxHeight = screenHeight * 0.85; // 最大高度为屏幕高度的85%

    return Container(
      constraints: BoxConstraints(
        maxHeight: maxHeight,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 固定的标题栏
            Padding(
              padding: const EdgeInsets.all(20),
              child: Stack(
                children: [
                  // 居中的标题
                  const Center(
                    child: Text(
                      '校验重单',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  // 右上角关闭按钮
                  Positioned(
                    right: 0,
                    child: GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        child: const Icon(
                          Icons.close,
                          size: 20,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 可滚动的内容区域
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    // 客户基本信息
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF7F8FA),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        children: [
                          _buildInfoRow('会员ID',
                              _getCustomerInfo('customerNumber') ?? '-'),
                          _buildInfoRow(
                              '客户姓名', _getCustomerInfo('name') ?? '-'),
                          _buildInfoRow('客户电话', phone),
                          _buildInfoRow(
                              '客户类型',
                              _getMemberTypeText(
                                  _getCustomerInfo('memberType'))),
                          _buildInfoRow(
                              '登记者', _getCustomerInfo('registerUser') ?? '-'),
                          _buildInfoRow(
                              '创建时间', _getCustomerInfo('entryTime') ?? '-'),
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),

                    // 项目信息标题
                    const Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        '项目信息',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // 项目信息
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF7F8FA),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        children: _buildProjectInfoList(context),
                      ),
                    ),

                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 1,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.left,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRowWithButton(String label, String value,
      Map<String, dynamic> project, BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 1,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.left,
            ),
          ),
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.right,
                ),
                const SizedBox(height: 8),
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                    // 拼接项目ID字符串
                    final projectId = project['projectId']?.toString() ?? '';
                    final serviceId = project['serviceId']?.toString() ?? '';
                    final customerId =
                        orderData['customerInfo']?['customerId']?.toString() ??
                            '';
                    final projectIds = '$projectId,$serviceId,$customerId';

                    // 跳转到跟单记录页面，传递项目ID字符串
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => FollowUpRecordPage(
                          customerInfo: orderData['customerInfo'],
                          serviceInfo: [project], // 只传递当前项目
                          projectIds: projectIds, // 传递拼接的ID字符串
                        ),
                      ),
                    );
                  },
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                    decoration: BoxDecoration(
                      color: Colors.black,
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      '跟单记录',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12.sp,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建项目信息列表
  List<Widget> _buildProjectInfoList(BuildContext context) {
    final serviceInfo = orderData['serviceInfo'];
    if (serviceInfo is List && serviceInfo.isNotEmpty) {
      List<Widget> projectWidgets = [];

      for (int i = 0; i < serviceInfo.length; i++) {
        final project = serviceInfo[i];
        if (project is Map<String, dynamic>) {
          // 添加项目信息
          projectWidgets.addAll([
            _buildInfoRow('客户编号', project['projectNumber']?.toString() ?? '-'),
            _buildInfoRow('地址', _getCustomerInfo('regionName') ?? '-'),
            _buildInfoRow(
                '首次来源/明细', project['firstSourceName']?.toString() ?? '-'),
            _buildInfoRow('来源/明细', project['sourceName']?.toString() ?? '-'),
            _buildInfoRow('客服经理/设计师', _getProjectServiceManagerText(project)),
            _buildInfoRow('渠道客服', '-'),
            _buildInfoRow('工程经理/项目经理',
                project['engineeringStewardRealName']?.toString() ?? '-'),
            _buildInfoRowWithButton(
                '商务经理/导购',
                project['shopGuideUserName']?.toString() ?? '-',
                project,
                context),
          ]);

          // 如果不是最后一个项目，添加间距
          if (i < serviceInfo.length - 1) {
            projectWidgets.add(SizedBox(height: 16.h));
          }
        }
      }

      return projectWidgets;
    }

    // 如果没有项目信息，返回空状态
    return [
      const Text(
        '暂无项目信息',
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey,
        ),
        textAlign: TextAlign.center,
      ),
    ];
  }

  // 获取客户信息
  String? _getCustomerInfo(String key) {
    final customerInfo = orderData['customerInfo'];
    if (customerInfo is Map<String, dynamic>) {
      return customerInfo[key]?.toString();
    }
    return null;
  }

  // 获取会员类型文本
  String _getMemberTypeText(String? memberType) {
    switch (memberType) {
      case '0':
        return '普通用户';
      case '1':
        return 'VIP用户';
      default:
        return '普通用户';
    }
  }

  // 获取项目的客服经理/设计师信息
  String _getProjectServiceManagerText(Map<String, dynamic> project) {
    final customerService = project['customerServiceUserName']?.toString();
    final designer = project['designerUserName']?.toString();

    if (customerService != null && designer != null) {
      return '$customerService/$designer';
    } else if (customerService != null) {
      return customerService;
    } else if (designer != null) {
      return designer;
    } else {
      return '-';
    }
  }
}
