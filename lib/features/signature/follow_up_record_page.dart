import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:oktoast/oktoast.dart';

class FollowUpRecordPage extends StatefulWidget {
  final Map<String, dynamic>? customerInfo;
  final List<dynamic>? serviceInfo;
  final String? projectIds;

  const FollowUpRecordPage({
    Key? key,
    this.customerInfo,
    this.serviceInfo,
    this.projectIds,
  }) : super(key: key);

  @override
  State<FollowUpRecordPage> createState() => _FollowUpRecordPageState();
}

class _FollowUpRecordPageState extends State<FollowUpRecordPage> {
  List<Map<String, dynamic>> followUpRecords = [];

  @override
  void initState() {
    super.initState();
    _loadFollowUpRecords();
  }

  void _loadFollowUpRecords() {
    // 初始化为空数组
    followUpRecords = [];

    // 如果有项目ID，调用API获取真实的跟单记录
    if (widget.projectIds != null && widget.projectIds!.isNotEmpty) {
      print('项目ID信息: ${widget.projectIds}');
      _fetchFollowUpRecords(widget.projectIds!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: const Text('跟单记录'),
        titleTextStyle: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 18.sp,
          color: Colors.black,
        ),
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.black,
        elevation: 0,
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.transparent,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0.0, 0.8],
            colors: [
              Color(0xFFFFF4E0), // #FFF4E0 顶部颜色
              Colors.white, // 白色 底部颜色
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: ListView.builder(
                  padding: EdgeInsets.all(16.w),
                  itemCount: followUpRecords.length,
                  itemBuilder: (context, index) {
                    final record = followUpRecords[index];
                    return _buildRecordItem(record);
                  },
                ),
              ),
              _buildAddRecordButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecordItem(Map<String, dynamic> record) {
    return Column(
      children: [
        // 时间容器 - 独立的白色背景，自适应宽度
        Align(
          alignment: Alignment.centerLeft,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Text(
              record['date'] ?? '',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        SizedBox(height: 8.h), // 时间与内容的间距
        // 内容容器 - 独立的白色背景
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // 操作人
              _buildInfoRow('操作人', record['operator'] ?? ''),
              SizedBox(height: 8.h),
              // 操作记录
              _buildInfoRow('操作记录', record['content'] ?? ''),
              SizedBox(height: 8.h),
              // 分类
              _buildInfoRow('分类', record['category'] ?? ''),
            ],
          ),
        ),
        SizedBox(height: 24.h), // 记录项之间的间距
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey[600],
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.black87,
              height: 1.4,
            ),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }

  Widget _buildAddRecordButton() {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: () {
            // 从 projectIds 中解析出 serviceId (格式: projectId,serviceId,customerId)
            String serviceId = '';
            if (widget.projectIds != null && widget.projectIds!.isNotEmpty) {
              final parts = widget.projectIds!.split(',');
              if (parts.length >= 2) {
                serviceId = parts[1]; // serviceId 是第二个部分
              }
            }
            _showAddRecordDialog(context, serviceId);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.black,
            padding: EdgeInsets.symmetric(vertical: 16.h),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(24.r),
            ),
          ),
          child: Text(
            '新增回访记录',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16.sp,
            ),
          ),
        ),
      ),
    );
  }

  //获取跟单记录
  Future<void> _fetchFollowUpRecords(String projectIds) async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/track/record',
        queryParameters: {'type': 1, 'logCode': '30004', 'bizId': projectIds},
      );
      if (response != null && mounted) {
        setState(() {
          // 处理API返回的数据结构，response就是data数组
          final List<dynamic> dataList = response as List<dynamic>? ?? [];
          followUpRecords = dataList.map((item) {
            final Map<String, dynamic> record = item as Map<String, dynamic>;
            return {
              'date': record['createTime'] ?? '',
              'operator': record['createBy'] ?? '',
              'content': record['content'] ?? '',
              'category': record['title'] ?? '',
            };
          }).toList();
        });
      }
    } catch (e) {
      print('获取跟单记录失败: $e');
    }
  }

  // 显示新增回访记录底推框
  void _showAddRecordDialog(BuildContext context, String serviceId) {
    final TextEditingController contentController = TextEditingController();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => GestureDetector(
        onTap: () {
          // 点击空白区域收起键盘
          FocusScope.of(context).unfocus();
        },
        child: Container(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.85,
          ),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 固定的标题栏
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Stack(
                    children: [
                      // 居中的标题
                      const Center(
                        child: Text(
                          '新增回访记录',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      // 右上角关闭按钮
                      Positioned(
                        right: 0,
                        child: GestureDetector(
                          onTap: () => Navigator.of(context).pop(),
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            child: const Icon(
                              Icons.close,
                              size: 20,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // 可滚动的内容区域
                Flexible(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 分类输入
                        RichText(
                          text: const TextSpan(
                            children: [
                              TextSpan(
                                text: '*',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.red,
                                ),
                              ),
                              TextSpan(
                                text: '分类',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black87,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          width: double.infinity,
                          height: 44,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                          decoration: BoxDecoration(
                            color: const Color(0xFFF7F8FA),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              '回访',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.black87,
                              ),
                            ),
                          ),
                        ),

                        const SizedBox(height: 24),

                        // 操作记录输入
                        RichText(
                          text: const TextSpan(
                            children: [
                              TextSpan(
                                text: '*',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.red,
                                ),
                              ),
                              TextSpan(
                                text: '操作记录',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black87,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          width: double.infinity,
                          height: 120,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                          decoration: BoxDecoration(
                            color: const Color(0xFFF7F8FA),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: TextField(
                            controller: contentController,
                            maxLines: null,
                            expands: true,
                            textAlignVertical: TextAlignVertical.top,
                            decoration: const InputDecoration(
                              hintText: '请输入',
                              hintStyle: TextStyle(color: Colors.grey),
                              border: InputBorder.none,
                            ),
                          ),
                        ),

                        const SizedBox(height: 40),
                      ],
                    ),
                  ),
                ),

                // 固定的底部按钮
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            side: const BorderSide(color: Colors.grey),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(24),
                            ),
                          ),
                          child: const Text(
                            '取消',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            // 获取用户输入的操作记录内容
                            final content = contentController.text.trim();
                            if (content.isEmpty) {
                              showToast('请输入操作记录');
                              return;
                            }
                            // 提交新增回访记录
                            _addFollowUpRecord(content, serviceId);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.black,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(24),
                            ),
                          ),
                          child: const Text(
                            '确认',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  //新增回访记录
  Future<void> _addFollowUpRecord(String content, String serviceId) async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.post(
        '/api/signature/visit/record/commit',
        data: {
          'title': '回访',
          'content': content,
          'bizId': serviceId,
        },
      );
      if (response != null && mounted) {
        showToast('新增回访记录成功');
        // 刷新列表
        _fetchFollowUpRecords(widget.projectIds!);
        Navigator.pop(context);
      }
    } catch (e) {
      showToast('新增回访记录失败');
    }
  }
}
